<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sd_tenants', function (Blueprint $table) {
            $table->id();
            $table->string('domain')->unique();
            $table->string('status', 60)->default('pending');
            $table->text('data')->nullable();
            $table->timestamps();
        });

        Schema::create('sd_tenant_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('sd_tenants')->onDelete('cascade');
            $table->string('key');
            $table->text('value')->nullable();
            $table->timestamps();
        });

        Schema::create('sd_members', function (Blueprint $table): void {
            $table->id();
            $table->string('username', 50)->unique();
            $table->string('full_name', 250)->nullable();
            $table->string('gender', 20)->nullable();
            $table->string('email')->unique();
            $table->string('password');
            $table->foreignId('avatar_id')->nullable();
            $table->date('dob')->nullable();
            $table->string('phone', 25)->nullable();
            $table->dateTime('confirmed_at')->nullable();
            $table->string('email_verify_token', 120)->nullable();
            $table->string('api_key')->nullable();
            $table->decimal('balance', 15, 2)->default(0);
            $table->decimal('total_spent', 15, 2)->default(0);
            $table->decimal('total_deposit', 15, 2)->default(0);
            $table->string('status', 60)->default('active');
            $table->string('membership_level')->default('member');
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('sd_member_password_resets', function (Blueprint $table): void {
            $table->string('email')->index();
            $table->string('token')->index();
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sd_member_activity_logs', function (Blueprint $table): void {
            $table->id();
            $table->string('action', 120);
            $table->text('user_agent')->nullable();
            $table->string('reference_url')->nullable();
            $table->string('reference_name')->nullable();
            $table->ipAddress()->nullable();
            $table->foreignId('member_id')->constrained('sd_members')->cascadeOnDelete();
            $table->timestamps();
        });

        Schema::create('sd_tenant_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('sd_tenants')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('sd_members')->cascadeOnDelete();
            $table->string('role', 20)->default('user');
            $table->unique(['tenant_id', 'user_id']);
        });

        Schema::create('sd_platforms', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('icon')->nullable();
            $table->string('description')->nullable();
            $table->string('status')->default('published');
            $table->timestamps();
            $table->index('status');
        });

        Schema::create('sd_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('platform_id')->constrained('sd_platforms')->onDelete('cascade');
            $table->string('name')->unique();
            $table->string('description')->nullable();
            $table->string('status')->default('published');
            $table->timestamps();
            $table->index('status');
        });

        Schema::create('sd_package_lists',function (Blueprint $table){
            $table->id();
            $table->string('name');
            $table->string('value')->unique();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });
        Schema::create('sd_packages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('sd_tenants')->cascadeOnDelete();
            $table->foreignId('package_list_id')->constrained('sd_package_lists')->cascadeOnDelete();
            $table->foreignId('category_id')->constrained('sd_categories')->cascadeOnDelete();
            $table->string('info');
            $table->string('mode')->default('api');
            $table->decimal('rate_member', 15, 2)->default(0);
            $table->decimal('rate_collaborator', 15, 2)->default(0);
            $table->decimal('rate_agency', 15, 2)->default(0);
            $table->decimal('rate_distributor', 15, 2)->default(0);
            $table->integer('min');
            $table->integer('max');
            $table->boolean('allow_reaction')->default(false);
            $table->boolean('allow_comment')->default(false);
            $table->boolean('allow_gender')->default(false);
            $table->boolean('get_uid')->default(false);
            $table->boolean('is_refund')->default(false);
            $table->boolean('is_warranty')->default(false);
            $table->boolean('allow_cancel')->default(false);
            $table->text('description')->nullable();
            $table->string('status')->default('active');
            $table->boolean('visibility')->default(true);
            $table->timestamps();
            $table->index('status');
        });

        Schema::create('sd_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('package_id')->constrained('sd_packages')->onDelete('cascade');
            $table->string('code')->unique();
            $table->string('url');
            $table->integer('count')->default(0);
            $table->string('status')->default('pending');
            $table->decimal('price', 10, 2)->default('0.00');
            $table->decimal('total', 10, 2)->default('0.00');
            $table->text('data')->nullable();
            $table->string('note')->nullable();
            $table->timestamps();
            $table->index('status');
            $table->index('code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sd_orders');
        Schema::dropIfExists('sd_packages');
        Schema::dropIfExists('sd_package_lists');
        Schema::dropIfExists('sd_categories');
        Schema::dropIfExists('sd_platforms');
        Schema::dropIfExists('sd_tenant_users');
        Schema::dropIfExists('sd_member_activity_logs');
        Schema::dropIfExists('sd_member_password_resets');
        Schema::dropIfExists('sd_members');
        Schema::dropIfExists('sd_tenants');
        Schema::dropIfExists('sd_tenant');
        Schema::dropIfExists('sd_tenants');
    }
};
