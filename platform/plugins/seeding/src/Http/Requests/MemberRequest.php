<?php

namespace Bo<PERSON>ble\Seeding\Http\Requests;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use <PERSON><PERSON>ble\Base\Rules\EmailRule;
use <PERSON><PERSON>ble\Base\Rules\MediaImageRule;
use Bo<PERSON><PERSON>\Seeding\Enums\MembershipLevelEnum;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class MemberRequest extends Request
{
    public function rules(): array
    {
        $rules = [
            'username' => ['required', 'string', 'max:60', 'alpha_dash', Rule::unique('sd_members', 'username')->ignore($this->route('member'))],
            'full_name' => ['required', 'string', 'max:120'],
            'email' => ['required', new EmailRule(), Rule::unique('sd_members', 'email')->ignore($this->route('member'))],
            'phone' => ['nullable', 'string', 'max:25'],
            'gender' => ['nullable', Rule::in(['male', 'female', 'other'])],
            'dob' => ['nullable', 'date', 'before:today'],
            'membership_level' => ['required', Rule::in(MembershipLevelEnum::values())],
            'balance' => ['nullable', 'numeric', 'min:0'],
            'total_spent' => ['nullable', 'numeric', 'min:0'],
            'total_deposit' => ['nullable', 'numeric', 'min:0'],
            'avatar_id' => ['nullable', 'string', new MediaImageRule()],
            'status' => ['required', Rule::in(BaseStatusEnum::values())],
        ];

        if (! $this->route('member')) {
            $rules['password'] = ['required', 'string', 'min:6', 'max:60'];
        }

        return $rules;
    }

    public function attributes(): array
    {
        return [
            'username' => trans('plugins/seeding::member.username'),
            'full_name' => trans('plugins/seeding::member.full_name'),
            'email' => trans('core/base::forms.email'),
            'phone' => trans('plugins/seeding::member.phone'),
            'gender' => trans('plugins/seeding::member.gender'),
            'dob' => trans('plugins/seeding::member.dob'),
            'membership_level' => trans('plugins/seeding::member.membership_level'),
            'balance' => trans('plugins/seeding::member.balance'),
            'total_spent' => trans('plugins/seeding::member.total_spent'),
            'total_deposit' => trans('plugins/seeding::member.total_deposit'),
            'avatar_id' => trans('plugins/seeding::member.avatar'),
            'password' => trans('plugins/seeding::member.password'),
            'status' => trans('core/base::tables.status'),
        ];
    }
}
