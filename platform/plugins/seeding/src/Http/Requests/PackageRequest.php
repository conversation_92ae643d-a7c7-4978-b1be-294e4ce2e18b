<?php

namespace Bo<PERSON>ble\Seeding\Http\Requests;

use Bo<PERSON>ble\Base\Rules\OnOffRule;
use Bo<PERSON>ble\Seeding\Enums\PackageStatusEnum;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PackageRequest extends Request
{
    public function rules(): array
    {
        return [
            'tenant_id' => ['required', 'integer', Rule::exists('sd_tenants', 'id')],
            'package_list_id' => ['required', 'integer', Rule::exists('sd_package_lists', 'id')],
            'category_id' => ['required', 'integer', Rule::exists('sd_categories', 'id')],
            'info' => ['required', 'string', 'max:255'],
            'mode' => ['nullable', 'string', 'max:100'],
            'rate_member' => ['nullable', 'numeric', 'min:0'],
            'rate_collaborator' => ['nullable', 'numeric', 'min:0'],
            'rate_agency' => ['nullable', 'numeric', 'min:0'],
            'rate_distributor' => ['nullable', 'numeric', 'min:0'],
            'min' => ['nullable', 'integer', 'min:0'],
            'max' => ['nullable', 'integer', 'min:0'],
            'allow_reaction' => [new OnOffRule()],
            'allow_comment' => [new OnOffRule()],
            'allow_gender' => [new OnOffRule()],
            'get_uid' => [new OnOffRule()],
            'is_refund' => [new OnOffRule()],
            'is_warranty' => [new OnOffRule()],
            'allow_cancel' => [new OnOffRule()],
            'description' => ['nullable', 'string', 'max:1000'],
            'status' => ['required', Rule::in(PackageStatusEnum::values())],
            'visibility' => ['nullable', Rule::in(['public', 'private'])],
        ];
    }

    public function attributes(): array
    {
        return [
            'tenant_id' => trans('plugins/seeding::package.tenant'),
            'package_list_id' => trans('plugins/seeding::package.package_list'),
            'category_id' => trans('plugins/seeding::package.category'),
            'info' => trans('plugins/seeding::package.info'),
            'mode' => trans('plugins/seeding::package.mode'),
            'rate_member' => trans('plugins/seeding::package.rate_member'),
            'rate_collaborator' => trans('plugins/seeding::package.rate_collaborator'),
            'rate_agency' => trans('plugins/seeding::package.rate_agency'),
            'rate_distributor' => trans('plugins/seeding::package.rate_distributor'),
            'min' => trans('plugins/seeding::package.min'),
            'max' => trans('plugins/seeding::package.max'),
            'allow_reaction' => trans('plugins/seeding::package.allow_reaction'),
            'allow_comment' => trans('plugins/seeding::package.allow_comment'),
            'allow_gender' => trans('plugins/seeding::package.allow_gender'),
            'get_uid' => trans('plugins/seeding::package.get_uid'),
            'is_refund' => trans('plugins/seeding::package.is_refund'),
            'is_warranty' => trans('plugins/seeding::package.is_warranty'),
            'allow_cancel' => trans('plugins/seeding::package.allow_cancel'),
            'description' => trans('plugins/seeding::package.description'),
            'status' => trans('core/base::tables.status'),
            'visibility' => trans('plugins/seeding::package.visibility'),
        ];
    }
}
