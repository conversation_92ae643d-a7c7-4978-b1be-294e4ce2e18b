<?php

namespace Bo<PERSON>ble\Seeding\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PlatformRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', Rule::unique('sd_platforms', 'name')->ignore($this->route('platform'))],
            'description' => ['nullable', 'string', 'max:1000'],
            'icon' => ['nullable', 'string', 'max:255'],
            'status' => ['required', Rule::in(BaseStatusEnum::values())],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('plugins/seeding::platform.name'),
            'description' => trans('plugins/seeding::platform.description'),
            'icon' => trans('plugins/seeding::platform.icon'),
            'status' => trans('core/base::tables.status'),
        ];
    }
}
