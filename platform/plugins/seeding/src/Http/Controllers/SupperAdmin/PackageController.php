<?php

namespace Bo<PERSON>ble\Seeding\Http\Controllers\SupperAdmin;

use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Supports\Breadcrumb;
use Bo<PERSON>ble\Seeding\Forms\PackageForm;
use Bo<PERSON>ble\Seeding\Http\Requests\PackageRequest;
use Botble\Seeding\Models\Package;
use Bo<PERSON>ble\Seeding\Tables\PackageTable;

class PackageController extends BaseController
{
    protected function breadcrumb(): Breadcrumb
    {
        return parent::breadcrumb()
            ->add(trans('plugins/seeding::package.name'), route('seeding.packages.index'));
    }

    public function index(PackageTable $table)
    {
        $this->pageTitle(trans('plugins/seeding::package.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/seeding::package.create'));

        return PackageForm::create()->renderForm();
    }

    public function store(PackageRequest $request)
    {
        $form = PackageForm::create()
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.packages.index')
            ->setNextRoute('seeding.packages.edit', $form->getModel()->getKey())
            ->withCreatedSuccessMessage();
    }

    public function edit(Package $package)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $package->info]));

        return PackageForm::createFromModel($package)->renderForm();
    }

    public function update(Package $package, PackageRequest $request)
    {
        PackageForm::createFromModel($package)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.packages.index')
            ->withUpdatedSuccessMessage();
    }

    public function destroy(Package $package): DeleteResourceAction
    {
        return DeleteResourceAction::make($package);
    }
}
