<?php

namespace Bo<PERSON>ble\Seeding\Http\Controllers\SupperAdmin;

use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Supports\Breadcrumb;
use Bo<PERSON>ble\Seeding\Forms\MemberForm;
use Bo<PERSON>ble\Seeding\Http\Requests\MemberRequest;
use Botble\Seeding\Models\Member;
use Bo<PERSON>ble\Seeding\Tables\MemberTable;
use Illuminate\Support\Str;

class MemberController extends BaseController
{
    protected function breadcrumb(): Breadcrumb
    {
        return parent::breadcrumb()
            ->add(trans('plugins/seeding::member.name'), route('seeding.members.index'));
    }

    public function index(MemberTable $table)
    {
        $this->pageTitle(trans('plugins/seeding::member.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/seeding::member.create'));

        return MemberForm::create()->renderForm();
    }

    public function store(MemberRequest $request)
    {
        $form = MemberForm::create();

        $form->saving(function (MemberForm $form) use ($request): void {
            $data = $request->validated();
            
            // Generate API key for new member
            $data['api_key'] = Str::random(60);
            
            $form
                ->getModel()
                ->fill($data)
                ->save();
        });

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.members.index')
            ->setNextRoute('seeding.members.edit', $form->getModel()->getKey())
            ->withCreatedSuccessMessage();
    }

    public function edit(Member $member)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $member->full_name]));

        return MemberForm::createFromModel($member)->renderForm();
    }

    public function update(Member $member, MemberRequest $request)
    {
        MemberForm::createFromModel($member)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('seeding.members.index')
            ->withUpdatedSuccessMessage();
    }

    public function destroy(Member $member): DeleteResourceAction
    {
        return DeleteResourceAction::make($member);
    }
}
