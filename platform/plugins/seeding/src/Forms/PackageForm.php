<?php

namespace Bo<PERSON><PERSON>\Seeding\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NumberFieldOption;
use Botble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\OnOffField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Seeding\Http\Requests\PackageRequest;
use Botble\Seeding\Models\Category;
use Botble\Seeding\Models\Package;
use Bo<PERSON>ble\Seeding\Models\PackageList;
use Bo<PERSON>ble\Seeding\Models\Tenant;

class PackageForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Package::class)
            ->setValidatorClass(PackageRequest::class)
            ->columns()
            ->add(
                'tenant_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::package.tenant'))
                    ->required()
                    ->choices($this->getTenantChoices())
                    ->searchable()
                    ->colspan(1)
            )
            ->add(
                'package_list_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::package.package_list'))
                    ->required()
                    ->choices($this->getPackageListChoices())
                    ->searchable()
                    ->colspan(1)
            )
            ->add(
                'category_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::package.category'))
                    ->required()
                    ->choices($this->getCategoryChoices())
                    ->searchable()
                    ->colspan(2)
            )
            ->add(
                'info',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::package.info'))
                    ->required()
                    ->colspan(2)
            )
            ->add(
                'mode',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::package.mode'))
                    ->colspan(1)
            )
            ->add(
                'rate_member',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::package.rate_member'))
                    ->min(0)
                    ->step(0.01)
                    ->colspan(1)
            )
            ->add(
                'rate_collaborator',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::package.rate_collaborator'))
                    ->min(0)
                    ->step(0.01)
                    ->colspan(1)
            )
            ->add(
                'rate_agency',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::package.rate_agency'))
                    ->min(0)
                    ->step(0.01)
                    ->colspan(1)
            )
            ->add(
                'rate_distributor',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::package.rate_distributor'))
                    ->min(0)
                    ->step(0.01)
                    ->colspan(1)
            )
            ->add(
                'min',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::package.min'))
                    ->min(0)
                    ->colspan(1)
            )
            ->add(
                'max',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::package.max'))
                    ->min(0)
                    ->colspan(1)
            )
            ->add(
                'allow_reaction',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.allow_reaction'))
                    ->colspan(1)
            )
            ->add(
                'allow_comment',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.allow_comment'))
                    ->colspan(1)
            )
            ->add(
                'allow_gender',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.allow_gender'))
                    ->colspan(1)
            )
            ->add(
                'get_uid',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.get_uid'))
                    ->colspan(1)
            )
            ->add(
                'is_refund',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.is_refund'))
                    ->colspan(1)
            )
            ->add(
                'is_warranty',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.is_warranty'))
                    ->colspan(1)
            )
            ->add(
                'allow_cancel',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/seeding::package.allow_cancel'))
                    ->colspan(1)
            )
            ->add(
                'visibility',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::package.visibility'))
                    ->choices([
                        'public' => trans('plugins/seeding::package.visibility_public'),
                        'private' => trans('plugins/seeding::package.visibility_private'),
                    ])
                    ->colspan(1)
            )
            ->add('description', TextareaField::class, DescriptionFieldOption::make()->colspan(2))
            ->add('status', SelectField::class, StatusFieldOption::make()->colspan(2))
            ->setBreakFieldPoint('status');
    }

    protected function getTenantChoices(): array
    {
        return Tenant::query()
            ->pluck('name', 'id')
            ->all();
    }

    protected function getPackageListChoices(): array
    {
        return PackageList::query()
            ->wherePublished()
            ->pluck('name', 'id')
            ->all();
    }

    protected function getCategoryChoices(): array
    {
        return Category::query()
            ->with('platform')
            ->wherePublished()
            ->get()
            ->mapWithKeys(function ($category) {
                return [$category->id => $category->platform->name . ' - ' . $category->name];
            })
            ->all();
    }
}
