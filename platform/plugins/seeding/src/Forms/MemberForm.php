<?php

namespace Bo<PERSON>ble\Seeding\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\DatePickerFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\EmailFieldOption;
use Botble\Base\Forms\FieldOptions\MediaImageFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NumberFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\DatePickerField;
use Botble\Base\Forms\Fields\EmailField;
use Botble\Base\Forms\Fields\MediaImageField;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\PasswordField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Bo<PERSON><PERSON>\Seeding\Enums\MembershipLevelEnum;
use Bo<PERSON>ble\Seeding\Http\Requests\MemberRequest;
use Bo<PERSON>ble\Seeding\Models\Member;

class MemberForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Member::class)
            ->setValidatorClass(MemberRequest::class)
            ->columns()
            ->add(
                'username',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::member.username'))
                    ->required()
                    ->maxLength(60)
                    ->colspan(1)
            )
            ->add(
                'full_name',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::member.full_name'))
                    ->required()
                    ->maxLength(120)
                    ->colspan(1)
            )
            ->add(
                'email',
                EmailField::class,
                EmailFieldOption::make()
                    ->required()
                    ->colspan(1)
            )
            ->add(
                'phone',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::member.phone'))
                    ->maxLength(25)
                    ->colspan(1)
            )
            ->add(
                'gender',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::member.gender'))
                    ->choices([
                        'male' => trans('plugins/seeding::member.male'),
                        'female' => trans('plugins/seeding::member.female'),
                        'other' => trans('plugins/seeding::member.other'),
                    ])
                    ->colspan(1)
            )
            ->add(
                'dob',
                DatePickerField::class,
                DatePickerFieldOption::make()
                    ->label(trans('plugins/seeding::member.dob'))
                    ->colspan(1)
            )
            ->add(
                'membership_level',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/seeding::member.membership_level'))
                    ->choices(MembershipLevelEnum::labels())
                    ->required()
                    ->colspan(1)
            )
            ->add(
                'balance',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::member.balance'))
                    ->min(0)
                    ->step(0.01)
                    ->defaultValue(0)
                    ->colspan(1)
            )
            ->add(
                'total_spent',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::member.total_spent'))
                    ->min(0)
                    ->step(0.01)
                    ->defaultValue(0)
                    ->colspan(1)
            )
            ->add(
                'total_deposit',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(trans('plugins/seeding::member.total_deposit'))
                    ->min(0)
                    ->step(0.01)
                    ->defaultValue(0)
                    ->colspan(1)
            )
            ->add(
                'avatar_id',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(trans('plugins/seeding::member.avatar'))
                    ->colspan(2)
            )
            ->when(! $this->getModel()->getKey(), function (MemberForm $form) {
                return $form
                    ->add(
                        'password',
                        PasswordField::class,
                        TextFieldOption::make()
                            ->label(trans('plugins/seeding::member.password'))
                            ->required()
                            ->maxLength(60)
                            ->colspan(2)
                    );
            })
            ->add('status', SelectField::class, StatusFieldOption::make()->colspan(2))
            ->setBreakFieldPoint('status');
    }
}
