<?php

namespace Bo<PERSON>ble\Seeding\Forms;

use Botble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Seeding\Http\Requests\PlatformRequest;
use Botble\Seeding\Models\Platform;

class PlatformForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Platform::class)
            ->setValidatorClass(PlatformRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required()->maxLength(255))
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'icon',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/seeding::platform.icon'))
                    ->placeholder('ti ti-brand-facebook')
                    ->helperText(trans('plugins/seeding::platform.icon_help'))
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
