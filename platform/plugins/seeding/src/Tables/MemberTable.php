<?php

namespace Bo<PERSON><PERSON>\Seeding\Tables;

use Bo<PERSON><PERSON>\Seeding\Models\Member;
use <PERSON><PERSON>ble\Table\Abstracts\TableAbstract;
use <PERSON><PERSON>ble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\EmailBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\EmailColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\ImageColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class MemberTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Member::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('seeding.members.create'))
            ->addColumns([
                IdColumn::make(),
                ImageColumn::make('avatar.url')
                    ->title(trans('plugins/seeding::member.avatar'))
                    ->width(60),
                NameColumn::make('full_name')
                    ->title(trans('plugins/seeding::member.full_name'))
                    ->route('seeding.members.edit'),
                Column::make('username')
                    ->title(trans('plugins/seeding::member.username'))
                    ->alignStart(),
                EmailColumn::make()->linkable(),
                Column::make('phone')
                    ->title(trans('plugins/seeding::member.phone'))
                    ->alignStart(),
                Column::make('membership_level')
                    ->title(trans('plugins/seeding::member.membership_level'))
                    ->alignCenter()
                    ->getValueUsing(function (Column $column) {
                        return $column->getItem()->membership_level->label();
                    }),
                Column::make('balance')
                    ->title(trans('plugins/seeding::member.balance'))
                    ->alignEnd()
                    ->getValueUsing(function (Column $column) {
                        return number_format($column->getItem()->balance, 2);
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('seeding.members.edit'),
                DeleteAction::make()->route('seeding.members.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('seeding.members.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make()->name('full_name'),
                EmailBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'username',
                        'full_name',
                        'email',
                        'phone',
                        'avatar_id',
                        'membership_level',
                        'balance',
                        'created_at',
                        'status',
                    ])
                    ->with(['avatar']);
            });
    }
}
