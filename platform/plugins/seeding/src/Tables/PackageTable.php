<?php

namespace Bo<PERSON><PERSON>\Seeding\Tables;

use Bo<PERSON>ble\Seeding\Models\Package;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class PackageTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Package::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('seeding.packages.create'))
            ->addColumns([
                IdColumn::make(),
                Column::make('info')
                    ->title(trans('plugins/seeding::package.info'))
                    ->alignStart()
                    ->route('seeding.packages.edit'),
                Column::make('tenant.name')
                    ->title(trans('plugins/seeding::package.tenant'))
                    ->alignStart(),
                Column::make('packageList.name')
                    ->title(trans('plugins/seeding::package.package_list'))
                    ->alignStart(),
                Column::make('category.name')
                    ->title(trans('plugins/seeding::package.category'))
                    ->alignStart(),
                Column::make('rate_member')
                    ->title(trans('plugins/seeding::package.rate_member'))
                    ->alignCenter(),
                Column::make('min')
                    ->title(trans('plugins/seeding::package.min'))
                    ->alignCenter(),
                Column::make('max')
                    ->title(trans('plugins/seeding::package.max'))
                    ->alignCenter(),
                Column::make('visibility')
                    ->title(trans('plugins/seeding::package.visibility'))
                    ->alignCenter()
                    ->getValueUsing(function (Column $column) {
                        $visibility = $column->getItem()->visibility;
                        return $visibility ? trans('plugins/seeding::package.visibility_' . $visibility) : '';
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('seeding.packages.edit'),
                DeleteAction::make()->route('seeding.packages.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('seeding.packages.destroy'),
            ])
            ->addBulkChanges([
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'tenant_id',
                        'package_list_id',
                        'category_id',
                        'info',
                        'rate_member',
                        'min',
                        'max',
                        'visibility',
                        'created_at',
                        'status',
                    ])
                    ->with(['tenant:id,name', 'packageList:id,name', 'category:id,name']);
            });
    }
}
