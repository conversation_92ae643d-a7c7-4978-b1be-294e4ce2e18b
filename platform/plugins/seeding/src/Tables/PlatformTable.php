<?php

namespace Bo<PERSON><PERSON>\Seeding\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Seeding\Models\Platform;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\FormattedColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class PlatformTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Platform::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('seeding.platforms.create'))
            ->addColumns([
                IdColumn::make(),
                FormattedColumn::make('icon')
                    ->title(trans('plugins/seeding::platform.icon'))
                    ->width(60)
                    ->alignCenter()
                    ->getValueUsing(function (FormattedColumn $column) {
                        $icon = $column->getItem()->icon;
                        return $icon ? '<i class="' . $icon . '"></i>' : '';
                    }),
                NameColumn::make()->route('seeding.platforms.edit'),
                Column::make('description')
                    ->title(trans('plugins/seeding::platform.description'))
                    ->alignStart()
                    ->limit(50),
                Column::make('categories_count')
                    ->title(trans('plugins/seeding::platform.categories_count'))
                    ->alignCenter()
                    ->getValueUsing(function (Column $column) {
                        return number_format($column->getItem()->categories_count);
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('seeding.platforms.edit'),
                DeleteAction::make()->route('seeding.platforms.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('seeding.platforms.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'description',
                        'icon',
                        'created_at',
                        'status',
                    ])
                    ->withCount('categories');
            });
    }
}
