<?php

use Illuminate\Support\Facades\Route;
use Botble\Base\Facades\AdminHelper;

Route::group(['namespace' => 'Botble\Seeding\Http\Controllers\SupperAdmin'], function (): void {
    AdminHelper::registerRoutes(function (): void {
        Route::group(['prefix' => 'seeding', 'as' => 'seeding.'], function (): void {
            // Categories routes
            Route::group(['prefix' => 'categories', 'as' => 'categories.'], function (): void {
                Route::resource('', 'CategoryController')->parameters(['' => 'category']);
            });

            // Platforms routes
            Route::group(['prefix' => 'platforms', 'as' => 'platforms.'], function (): void {
                Route::resource('', 'PlatformController')->parameters(['' => 'platform']);
            });

            // Package Lists routes
            Route::group(['prefix' => 'package-lists', 'as' => 'package-lists.'], function (): void {
                Route::resource('', 'PackageListController')->parameters(['' => 'package_list']);
            });

            // Packages routes
            Route::group(['prefix' => 'packages', 'as' => 'packages.'], function (): void {
                Route::resource('', 'PackageController')->parameters(['' => 'package']);
            });

            // Members routes
            Route::group(['prefix' => 'members', 'as' => 'members.'], function (): void {
                Route::resource('', 'MemberController')->parameters(['' => 'member']);
            });
        });
    });
});
