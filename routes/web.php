<?php

use App\Http\Controllers\Admin\WebsiteController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Website Management Routes (Admin)
Route::prefix('admin')->middleware(['auth'])->group(function () {
    Route::resource('websites', WebsiteController::class);
    Route::post('websites/{website}/set-default', [WebsiteController::class, 'setDefault'])->name('websites.set-default');
    Route::post('websites/{website}/toggle-active', [WebsiteController::class, 'toggleActive'])->name('websites.toggle-active');
    Route::get('websites/{website}/settings', [WebsiteController::class, 'getSettings'])->name('websites.settings');
    Route::put('websites/{website}/settings', [WebsiteController::class, 'updateSettings'])->name('websites.update-settings');
});

// API Routes for Website
Route::prefix('api')->group(function () {
    Route::get('websites', [WebsiteController::class, 'apiIndex']);
    Route::get('current-website-settings', [WebsiteController::class, 'getCurrentSettings']);
});
