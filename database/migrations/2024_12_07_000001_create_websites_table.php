<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (! Schema::hasTable('websites')) {
            Schema::create('websites', function (Blueprint $table): void {
                $table->id();
                $table->string('name')->comment('Tên website');
                $table->string('domain')->unique()->comment('Tên miền website');
                $table->string('code', 50)->unique()->comment('Mã website (dùng để phân biệt)');
                $table->text('description')->nullable()->comment('Mô tả website');
                $table->string('logo')->nullable()->comment('Logo website');
                $table->string('favicon')->nullable()->comment('Favicon website');
                $table->json('config')->nullable()->comment('<PERSON>ấu hình chung của website');
                $table->boolean('is_active')->default(true)->comment('Trạng thái hoạt động');
                $table->boolean('is_default')->default(false)->comment('Website mặc định');
                $table->integer('sort_order')->default(0)->comment('Thứ tự sắp xếp');
                $table->timestamps();
                
                $table->index(['is_active', 'sort_order']);
                $table->index('is_default');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('websites');
    }
};
