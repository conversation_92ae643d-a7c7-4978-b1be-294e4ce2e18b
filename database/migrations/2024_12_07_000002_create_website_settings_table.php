<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (! Schema::hasTable('website_settings')) {
            Schema::create('website_settings', function (Blueprint $table): void {
                $table->id();
                $table->foreignId('website_id')->constrained('websites')->onDelete('cascade');
                $table->string('key')->comment('Khóa cài đặt');
                $table->text('value')->nullable()->comment('Giá trị cài đặt');
                $table->string('type', 50)->default('string')->comment('Kiểu dữ liệu: string, json, boolean, integer, float');
                $table->text('description')->nullable()->comment('Mô tả cài đặt');
                $table->string('group', 100)->nullable()->comment('Nhóm cài đặt');
                $table->timestamps();
                
                $table->unique(['website_id', 'key']);
                $table->index(['website_id', 'group']);
                $table->index('key');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('website_settings');
    }
};
