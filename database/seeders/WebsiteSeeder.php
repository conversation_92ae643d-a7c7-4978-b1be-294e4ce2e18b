<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Website;
use App\Models\WebsiteSetting;

class WebsiteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo website mặc định
        $defaultWebsite = Website::create([
            'name' => 'Website Chính',
            'domain' => 'example.com',
            'code' => 'main',
            'description' => 'Website chính của hệ thống',
            'logo' => '/images/logo-main.png',
            'favicon' => '/images/favicon-main.ico',
            'config' => [
                'theme' => 'default',
                'language' => 'vi',
                'timezone' => 'Asia/Ho_Chi_Minh',
            ],
            'is_active' => true,
            'is_default' => true,
            'sort_order' => 1,
        ]);

        // Tạo website phụ 1
        $website2 = Website::create([
            'name' => 'Website Thương Mại',
            'domain' => 'shop.example.com',
            'code' => 'shop',
            'description' => 'Website bán hàng trực tuyến',
            'logo' => '/images/logo-shop.png',
            'favicon' => '/images/favicon-shop.ico',
            'config' => [
                'theme' => 'ecommerce',
                'language' => 'vi',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'currency' => 'VND',
            ],
            'is_active' => true,
            'is_default' => false,
            'sort_order' => 2,
        ]);

        // Tạo website phụ 2
        $website3 = Website::create([
            'name' => 'Website Tin Tức',
            'domain' => 'news.example.com',
            'code' => 'news',
            'description' => 'Website tin tức và blog',
            'logo' => '/images/logo-news.png',
            'favicon' => '/images/favicon-news.ico',
            'config' => [
                'theme' => 'news',
                'language' => 'vi',
                'timezone' => 'Asia/Ho_Chi_Minh',
            ],
            'is_active' => true,
            'is_default' => false,
            'sort_order' => 3,
        ]);

        // Thêm cài đặt cho website chính
        $this->addSettingsForWebsite($defaultWebsite->id, [
            // Cài đặt chung
            'site_title' => ['value' => 'Website Chính - Trang Chủ', 'type' => 'string', 'group' => 'general'],
            'site_description' => ['value' => 'Mô tả website chính của hệ thống', 'type' => 'string', 'group' => 'general'],
            'site_keywords' => ['value' => 'website, chính, hệ thống', 'type' => 'string', 'group' => 'general'],
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'string', 'group' => 'contact'],
            'contact_phone' => ['value' => '0123456789', 'type' => 'string', 'group' => 'contact'],
            
            // Cài đặt SEO
            'seo_title' => ['value' => 'Website Chính | Trang Chủ', 'type' => 'string', 'group' => 'seo'],
            'seo_description' => ['value' => 'Website chính của hệ thống với đầy đủ tính năng', 'type' => 'string', 'group' => 'seo'],
            'google_analytics_id' => ['value' => 'GA-XXXXXXXXX', 'type' => 'string', 'group' => 'seo'],
            
            // Cài đặt mạng xã hội
            'facebook_url' => ['value' => 'https://facebook.com/example', 'type' => 'string', 'group' => 'social'],
            'twitter_url' => ['value' => 'https://twitter.com/example', 'type' => 'string', 'group' => 'social'],
            'instagram_url' => ['value' => 'https://instagram.com/example', 'type' => 'string', 'group' => 'social'],
        ]);

        // Thêm cài đặt cho website thương mại
        $this->addSettingsForWebsite($website2->id, [
            // Cài đặt chung
            'site_title' => ['value' => 'Shop Online - Mua Sắm Trực Tuyến', 'type' => 'string', 'group' => 'general'],
            'site_description' => ['value' => 'Cửa hàng trực tuyến với hàng ngàn sản phẩm chất lượng', 'type' => 'string', 'group' => 'general'],
            'site_keywords' => ['value' => 'shop, mua sắm, trực tuyến, sản phẩm', 'type' => 'string', 'group' => 'general'],
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'string', 'group' => 'contact'],
            'contact_phone' => ['value' => '0987654321', 'type' => 'string', 'group' => 'contact'],
            
            // Cài đặt thương mại
            'currency' => ['value' => 'VND', 'type' => 'string', 'group' => 'ecommerce'],
            'tax_rate' => ['value' => '10', 'type' => 'float', 'group' => 'ecommerce'],
            'free_shipping_amount' => ['value' => '500000', 'type' => 'integer', 'group' => 'ecommerce'],
            'enable_reviews' => ['value' => '1', 'type' => 'boolean', 'group' => 'ecommerce'],
            
            // Cài đặt thanh toán
            'payment_methods' => ['value' => json_encode(['cod', 'bank_transfer', 'momo']), 'type' => 'json', 'group' => 'payment'],
        ]);

        // Thêm cài đặt cho website tin tức
        $this->addSettingsForWebsite($website3->id, [
            // Cài đặt chung
            'site_title' => ['value' => 'Tin Tức 24h - Cập Nhật Mọi Lúc', 'type' => 'string', 'group' => 'general'],
            'site_description' => ['value' => 'Website tin tức cập nhật nhanh nhất và chính xác nhất', 'type' => 'string', 'group' => 'general'],
            'site_keywords' => ['value' => 'tin tức, 24h, cập nhật, nhanh', 'type' => 'string', 'group' => 'general'],
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'string', 'group' => 'contact'],
            'contact_phone' => ['value' => '**********', 'type' => 'string', 'group' => 'contact'],
            
            // Cài đặt tin tức
            'posts_per_page' => ['value' => '12', 'type' => 'integer', 'group' => 'news'],
            'enable_comments' => ['value' => '1', 'type' => 'boolean', 'group' => 'news'],
            'auto_publish' => ['value' => '0', 'type' => 'boolean', 'group' => 'news'],
            'featured_categories' => ['value' => json_encode(['thoi-su', 'the-thao', 'giai-tri']), 'type' => 'json', 'group' => 'news'],
        ]);
    }

    /**
     * Thêm cài đặt cho website
     */
    private function addSettingsForWebsite(int $websiteId, array $settings): void
    {
        foreach ($settings as $key => $config) {
            WebsiteSetting::create([
                'website_id' => $websiteId,
                'key' => $key,
                'value' => $config['value'],
                'type' => $config['type'],
                'group' => $config['group'],
                'description' => $config['description'] ?? null,
            ]);
        }
    }
}
