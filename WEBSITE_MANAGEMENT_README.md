# Hệ Thống Quản Lý Nhiều Website

Hệ thống này cho phép quản lý nhiều website với cài đặt riêng biệt cho từng website theo dạng key-value.

## Tính Năng

- ✅ Quản lý nhiều website với domain riêng biệt
- ✅ Cài đặt theo từng website dạng key-value
- ✅ Tự động detect website hiện tại dựa trên domain
- ✅ Cache để tối ưu hiệu suất
- ✅ Helper functions tiện lợi
- ✅ Command line interface
- ✅ API endpoints
- ✅ Middleware tự động

## Cài Đặt

### 1. Chạy Migration

```bash
php artisan migrate
```

### 2. <PERSON><PERSON><PERSON> (T<PERSON><PERSON> chọn)

```bash
php artisan db:seed --class=WebsiteSeeder
```

### 3. Clear Cache

```bash
php artisan cache:clear
php artisan config:clear
```

## Cấu Trúc Database

### Bảng `websites`
- `id`: ID website
- `name`: <PERSON>ên website
- `domain`: Tê<PERSON>
- `code`: Mã website (unique)
- `description`: <PERSON><PERSON> tả
- `logo`: Đường dẫn logo
- `favicon`: Đường dẫn favicon
- `config`: Cấu hình JSON
- `is_active`: Trạng thái hoạt động
- `is_default`: Website mặc định
- `sort_order`: Thứ tự sắp xếp

### Bảng `website_settings`
- `id`: ID cài đặt
- `website_id`: ID website
- `key`: Khóa cài đặt
- `value`: Giá trị
- `type`: Kiểu dữ liệu (string, integer, float, boolean, json)
- `description`: Mô tả
- `group`: Nhóm cài đặt

## Sử Dụng

### Helper Functions

```php
// Lấy website hiện tại
$website = current_website();

// Lấy cài đặt của website hiện tại
$siteTitle = website_setting('site_title', 'Default Title');

// Lấy cài đặt theo nhóm
$generalSettings = website_settings_group('general');

// Lưu cài đặt
set_website_setting('site_title', 'New Title', 'string', 'Site title', 'general');
```

### Sử Dụng Model

```php
use App\Models\Website;
use App\Helpers\WebsiteHelper;

// Lấy website theo domain
$website = Website::getByDomain('example.com');

// Lấy website theo code
$website = Website::getByCode('main');

// Lấy cài đặt
$value = $website->getSetting('key', 'default');

// Lưu cài đặt
$website->setSetting('key', 'value', 'string', 'Description', 'group');

// Lấy cài đặt theo nhóm
$settings = $website->getSettingsByGroup('general');
```

### Command Line

```bash
# Liệt kê tất cả website
php artisan website:manage list

# Tạo website mới
php artisan website:manage create --name="New Site" --domain="newsite.com" --code="newsite"

# Cập nhật website
php artisan website:manage update --id=1 --name="Updated Name"

# Xóa website
php artisan website:manage delete --id=1

# Xem cài đặt website
php artisan website:manage settings --id=1

# Cập nhật cài đặt
php artisan website:manage settings --id=1 --key="site_title" --value="New Title" --type="string" --group="general"

# Xóa cache
php artisan website:manage clear-cache
```

### API Endpoints

```bash
# Lấy danh sách website
GET /api/websites

# Lấy cài đặt website hiện tại
GET /api/current-website-settings

# Quản lý website (Admin)
GET /admin/websites
POST /admin/websites
GET /admin/websites/{id}
PUT /admin/websites/{id}
DELETE /admin/websites/{id}

# Cài đặt website
GET /admin/websites/{id}/settings
PUT /admin/websites/{id}/settings
```

### Trong Blade Templates

```blade
{{-- Lấy website hiện tại --}}
@if($currentWebsite)
    <h1>{{ $currentWebsite->name }}</h1>
    <img src="{{ $currentWebsite->logo }}" alt="Logo">
@endif

{{-- Lấy cài đặt --}}
<title>{{ website_setting('site_title', 'Default Title') }}</title>
<meta name="description" content="{{ website_setting('site_description') }}">

{{-- Lấy cài đặt theo nhóm --}}
@php
    $socialSettings = website_settings_group('social');
@endphp

@if(!empty($socialSettings['facebook_url']))
    <a href="{{ $socialSettings['facebook_url'] }}">Facebook</a>
@endif
```

## Cấu Hình

File cấu hình: `config/website.php`

```php
return [
    'cache' => [
        'enabled' => true,
        'ttl' => 3600,
    ],
    'default_settings' => [
        'general' => [
            'site_title' => 'Website Title',
            'site_description' => 'Website Description',
        ],
    ],
    // ... các cấu hình khác
];
```

## Các Nhóm Cài Đặt Mặc Định

- `general`: Cài đặt chung
- `seo`: Tối ưu SEO
- `social`: Mạng xã hội
- `contact`: Thông tin liên hệ
- `ecommerce`: Thương mại điện tử
- `payment`: Thanh toán
- `news`: Tin tức
- `appearance`: Giao diện
- `advanced`: Nâng cao

## Kiểu Dữ Liệu Hỗ Trợ

- `string`: Chuỗi văn bản
- `text`: Văn bản dài
- `integer`: Số nguyên
- `float`: Số thực
- `boolean`: True/False
- `json`: Dữ liệu JSON
- `url`: Đường dẫn URL
- `email`: Địa chỉ email
- `phone`: Số điện thoại
- `color`: Mã màu hex
- `image`: Đường dẫn hình ảnh

## Cache

Hệ thống sử dụng cache để tối ưu hiệu suất:

```php
// Xóa cache cụ thể
WebsiteHelper::clearSettingCache($websiteId, $key, $group);

// Xóa tất cả cache
WebsiteHelper::clearAllCache();
```

## Lưu Ý

1. Chỉ có thể có 1 website mặc định
2. Không thể xóa website mặc định
3. Domain phải unique
4. Code phải unique và chỉ chứa a-z, 0-9, _, -
5. Cache được tự động xóa khi có thay đổi

## Ví Dụ Sử Dụng

### Tạo Website Mới

```php
$website = Website::create([
    'name' => 'Shop Online',
    'domain' => 'shop.example.com',
    'code' => 'shop',
    'description' => 'Website bán hàng',
    'is_active' => true,
]);

// Thêm cài đặt
$website->setSetting('site_title', 'Shop Online - Mua Sắm Trực Tuyến', 'string', null, 'general');
$website->setSetting('currency', 'VND', 'string', null, 'ecommerce');
$website->setSetting('tax_rate', 10, 'float', null, 'ecommerce');
```

### Middleware Tự Động

Middleware `DetectWebsiteMiddleware` sẽ tự động:
- Detect website hiện tại dựa trên domain
- Share biến `$currentWebsite` cho tất cả views
- Share function `$websiteSettings()` cho views

## Troubleshooting

### Lỗi Cache
```bash
php artisan cache:clear
php artisan website:manage clear-cache
```

### Lỗi Migration
```bash
php artisan migrate:fresh --seed
```

### Lỗi Autoload
```bash
composer dump-autoload
```
