<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class WebsiteSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'website_id',
        'key',
        'value',
        'type',
        'description',
        'group',
    ];

    protected $casts = [
        'website_id' => 'integer',
    ];

    /**
     * Quan hệ với bảng websites
     */
    public function website(): BelongsTo
    {
        return $this->belongsTo(Website::class);
    }

    /**
     * Lấy giá trị đã được cast theo type
     */
    public function getCastedValueAttribute()
    {
        switch ($this->type) {
            case 'boolean':
                return (bool) $this->value;
            case 'integer':
                return (int) $this->value;
            case 'float':
                return (float) $this->value;
            case 'json':
                return json_decode($this->value, true);
            default:
                return $this->value;
        }
    }

    /**
     * Scope: Lọc theo nhóm
     */
    public function scopeByGroup(Builder $query, string $group): Builder
    {
        return $query->where('group', $group);
    }

    /**
     * Scope: Lọc theo key
     */
    public function scopeByKey(Builder $query, string $key): Builder
    {
        return $query->where('key', $key);
    }

    /**
     * Scope: Lọc theo website
     */
    public function scopeByWebsite(Builder $query, int $websiteId): Builder
    {
        return $query->where('website_id', $websiteId);
    }

    /**
     * Lấy cài đặt theo website và key
     */
    public static function getByWebsiteAndKey(int $websiteId, string $key, $default = null)
    {
        $setting = static::byWebsite($websiteId)->byKey($key)->first();
        
        if (!$setting) {
            return $default;
        }

        return $setting->casted_value;
    }

    /**
     * Lưu hoặc cập nhật cài đặt
     */
    public static function setByWebsiteAndKey(
        int $websiteId, 
        string $key, 
        $value, 
        string $type = 'string', 
        string $description = null, 
        string $group = null
    ): self {
        return static::updateOrCreate(
            [
                'website_id' => $websiteId,
                'key' => $key,
            ],
            [
                'value' => is_array($value) || is_object($value) ? json_encode($value) : $value,
                'type' => $type,
                'description' => $description,
                'group' => $group,
            ]
        );
    }

    /**
     * Lấy tất cả cài đặt của một website theo nhóm
     */
    public static function getByWebsiteAndGroup(int $websiteId, string $group): array
    {
        $settings = static::byWebsite($websiteId)->byGroup($group)->get();
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->key] = $setting->casted_value;
        }
        
        return $result;
    }

    /**
     * Xóa cài đặt theo website và key
     */
    public static function deleteByWebsiteAndKey(int $websiteId, string $key): bool
    {
        return static::byWebsite($websiteId)->byKey($key)->delete();
    }
}
