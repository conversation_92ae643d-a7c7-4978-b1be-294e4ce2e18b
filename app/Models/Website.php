<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Website extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'domain',
        'code',
        'description',
        'logo',
        'favicon',
        'config',
        'is_active',
        'is_default',
        'sort_order',
    ];

    protected $casts = [
        'config' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * <PERSON>uan hệ với bảng website_settings
     */
    public function settings(): HasMany
    {
        return $this->hasMany(WebsiteSetting::class);
    }

    /**
     * Lấy cài đặt theo key
     */
    public function getSetting(string $key, $default = null)
    {
        $setting = $this->settings()->where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return $this->castSettingValue($setting->value, $setting->type);
    }

    /**
     * Lưu cài đặt
     */
    public function setSetting(string $key, $value, string $type = 'string', string $description = null, string $group = null): void
    {
        $this->settings()->updateOrCreate(
            ['key' => $key],
            [
                'value' => is_array($value) || is_object($value) ? json_encode($value) : $value,
                'type' => $type,
                'description' => $description,
                'group' => $group,
            ]
        );
    }

    /**
     * Lấy nhiều cài đặt theo nhóm
     */
    public function getSettingsByGroup(string $group): array
    {
        $settings = $this->settings()->where('group', $group)->get();
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->key] = $this->castSettingValue($setting->value, $setting->type);
        }
        
        return $result;
    }

    /**
     * Chuyển đổi giá trị theo kiểu dữ liệu
     */
    private function castSettingValue($value, string $type)
    {
        switch ($type) {
            case 'boolean':
                return (bool) $value;
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Scope: Chỉ lấy website đang hoạt động
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Sắp xếp theo thứ tự
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Lấy website mặc định
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Lấy website theo domain
     */
    public static function getByDomain(string $domain): ?self
    {
        return static::where('domain', $domain)->active()->first();
    }

    /**
     * Lấy website theo code
     */
    public static function getByCode(string $code): ?self
    {
        return static::where('code', $code)->active()->first();
    }

    /**
     * Đặt làm website mặc định
     */
    public function setAsDefault(): void
    {
        // Bỏ default của tất cả website khác
        static::where('is_default', true)->update(['is_default' => false]);
        
        // Đặt website này làm default
        $this->update(['is_default' => true]);
    }
}
