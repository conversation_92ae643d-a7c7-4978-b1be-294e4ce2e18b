<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Helpers\WebsiteHelper;
use Symfony\Component\HttpFoundation\Response;

class DetectWebsiteMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Tự động detect website hiện tại dựa trên domain
        $currentWebsite = WebsiteHelper::getCurrentWebsite();
        
        // Chia sẻ thông tin website với tất cả views
        if ($currentWebsite) {
            view()->share('currentWebsite', $currentWebsite);
            view()->share('websiteSettings', function($group = null) use ($currentWebsite) {
                if ($group) {
                    return WebsiteHelper::getSettingsByGroup($group);
                }
                return $currentWebsite->settings()->get()->keyBy('key');
            });
        }

        return $next($request);
    }
}
