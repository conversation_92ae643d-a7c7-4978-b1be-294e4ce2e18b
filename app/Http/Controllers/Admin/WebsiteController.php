<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Website;
use App\Models\WebsiteSetting;
use App\Helpers\WebsiteHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class WebsiteController extends Controller
{
    /**
     * Hiển thị danh sách website
     */
    public function index(): View
    {
        $websites = Website::with('settings')->ordered()->paginate(20);
        
        return view('admin.websites.index', compact('websites'));
    }

    /**
     * Hiển thị form tạo website mới
     */
    public function create(): View
    {
        return view('admin.websites.create');
    }

    /**
     * Lưu website mới
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:websites,domain',
            'code' => 'required|string|max:50|unique:websites,code',
            'description' => 'nullable|string',
            'logo' => 'nullable|string',
            'favicon' => 'nullable|string',
            'config' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $website = Website::create($request->all());

        if ($request->is_default) {
            $website->setAsDefault();
        }

        // Xóa cache
        WebsiteHelper::clearAllCache();

        return response()->json([
            'success' => true,
            'message' => 'Website đã được tạo thành công!',
            'data' => $website
        ]);
    }

    /**
     * Hiển thị chi tiết website
     */
    public function show(Website $website): View
    {
        $website->load('settings');
        
        // Nhóm settings theo group
        $settingGroups = $website->settings->groupBy('group');
        
        return view('admin.websites.show', compact('website', 'settingGroups'));
    }

    /**
     * Hiển thị form chỉnh sửa website
     */
    public function edit(Website $website): View
    {
        $website->load('settings');
        
        return view('admin.websites.edit', compact('website'));
    }

    /**
     * Cập nhật website
     */
    public function update(Request $request, Website $website): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:websites,domain,' . $website->id,
            'code' => 'required|string|max:50|unique:websites,code,' . $website->id,
            'description' => 'nullable|string',
            'logo' => 'nullable|string',
            'favicon' => 'nullable|string',
            'config' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $website->update($request->all());

        if ($request->is_default) {
            $website->setAsDefault();
        }

        // Xóa cache
        WebsiteHelper::clearAllCache();

        return response()->json([
            'success' => true,
            'message' => 'Website đã được cập nhật thành công!',
            'data' => $website
        ]);
    }

    /**
     * Xóa website
     */
    public function destroy(Website $website): JsonResponse
    {
        if ($website->is_default) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa website mặc định!'
            ], 400);
        }

        $website->delete();

        // Xóa cache
        WebsiteHelper::clearAllCache();

        return response()->json([
            'success' => true,
            'message' => 'Website đã được xóa thành công!'
        ]);
    }

    /**
     * Lấy cài đặt của website
     */
    public function getSettings(Website $website): JsonResponse
    {
        $settings = $website->settings()->get()->groupBy('group');
        
        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Cập nhật cài đặt website
     */
    public function updateSettings(Request $request, Website $website): JsonResponse
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'nullable',
            'settings.*.type' => 'required|string|in:string,integer,float,boolean,json',
            'settings.*.description' => 'nullable|string',
            'settings.*.group' => 'nullable|string',
        ]);

        foreach ($request->settings as $settingData) {
            $website->setSetting(
                $settingData['key'],
                $settingData['value'],
                $settingData['type'],
                $settingData['description'] ?? null,
                $settingData['group'] ?? null
            );
        }

        // Xóa cache
        WebsiteHelper::clearAllCache();

        return response()->json([
            'success' => true,
            'message' => 'Cài đặt đã được cập nhật thành công!'
        ]);
    }

    /**
     * Đặt làm website mặc định
     */
    public function setDefault(Website $website): JsonResponse
    {
        $website->setAsDefault();

        // Xóa cache
        WebsiteHelper::clearAllCache();

        return response()->json([
            'success' => true,
            'message' => 'Đã đặt làm website mặc định!'
        ]);
    }

    /**
     * Toggle trạng thái active
     */
    public function toggleActive(Website $website): JsonResponse
    {
        $website->update(['is_active' => !$website->is_active]);

        // Xóa cache
        WebsiteHelper::clearAllCache();

        return response()->json([
            'success' => true,
            'message' => 'Trạng thái website đã được cập nhật!',
            'is_active' => $website->is_active
        ]);
    }

    /**
     * Lấy danh sách website cho API
     */
    public function apiIndex(): JsonResponse
    {
        $websites = Website::active()->ordered()->get();
        
        return response()->json([
            'success' => true,
            'data' => $websites
        ]);
    }

    /**
     * Lấy cài đặt website hiện tại
     */
    public function getCurrentSettings(): JsonResponse
    {
        $website = WebsiteHelper::getCurrentWebsite();
        
        if (!$website) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy website hiện tại!'
            ], 404);
        }

        $settings = $website->settings()->get()->groupBy('group');
        
        return response()->json([
            'success' => true,
            'data' => [
                'website' => $website,
                'settings' => $settings
            ]
        ]);
    }
}
