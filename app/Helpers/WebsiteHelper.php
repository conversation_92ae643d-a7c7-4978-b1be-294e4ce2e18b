<?php

namespace App\Helpers;

use App\Models\Website;
use App\Models\WebsiteSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;

class WebsiteHelper
{
    /**
     * Cache key prefix
     */
    const CACHE_PREFIX = 'website_';
    const CACHE_TTL = 3600; // 1 hour

    /**
     * Current website instance
     */
    private static ?Website $currentWebsite = null;

    /**
     * Lấy website hiện tại dựa trên domain
     */
    public static function getCurrentWebsite(): ?Website
    {
        if (self::$currentWebsite !== null) {
            return self::$currentWebsite;
        }

        $domain = Request::getHost();
        
        // Tìm trong cache trước
        $cacheKey = self::CACHE_PREFIX . 'domain_' . md5($domain);
        self::$currentWebsite = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($domain) {
            return Website::getByDomain($domain) ?? Website::getDefault();
        });

        return self::$currentWebsite;
    }

    /**
     * Lấy website theo code
     */
    public static function getWebsiteByCode(string $code): ?Website
    {
        $cacheKey = self::CACHE_PREFIX . 'code_' . $code;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($code) {
            return Website::getByCode($code);
        });
    }

    /**
     * Lấy tất cả website đang hoạt động
     */
    public static function getActiveWebsites(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'active_all';
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return Website::active()->ordered()->get()->toArray();
        });
    }

    /**
     * Lấy cài đặt của website hiện tại
     */
    public static function getSetting(string $key, $default = null)
    {
        $website = self::getCurrentWebsite();
        
        if (!$website) {
            return $default;
        }

        $cacheKey = self::CACHE_PREFIX . 'setting_' . $website->id . '_' . $key;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($website, $key, $default) {
            return $website->getSetting($key, $default);
        });
    }

    /**
     * Lấy cài đặt theo nhóm của website hiện tại
     */
    public static function getSettingsByGroup(string $group): array
    {
        $website = self::getCurrentWebsite();
        
        if (!$website) {
            return [];
        }

        $cacheKey = self::CACHE_PREFIX . 'group_' . $website->id . '_' . $group;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($website, $group) {
            return $website->getSettingsByGroup($group);
        });
    }

    /**
     * Lưu cài đặt cho website hiện tại
     */
    public static function setSetting(
        string $key, 
        $value, 
        string $type = 'string', 
        string $description = null, 
        string $group = null
    ): void {
        $website = self::getCurrentWebsite();
        
        if (!$website) {
            return;
        }

        $website->setSetting($key, $value, $type, $description, $group);
        
        // Xóa cache
        self::clearSettingCache($website->id, $key, $group);
    }

    /**
     * Lấy cài đặt của website cụ thể
     */
    public static function getWebsiteSetting(int $websiteId, string $key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX . 'setting_' . $websiteId . '_' . $key;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($websiteId, $key, $default) {
            return WebsiteSetting::getByWebsiteAndKey($websiteId, $key, $default);
        });
    }

    /**
     * Lưu cài đặt cho website cụ thể
     */
    public static function setWebsiteSetting(
        int $websiteId,
        string $key, 
        $value, 
        string $type = 'string', 
        string $description = null, 
        string $group = null
    ): void {
        WebsiteSetting::setByWebsiteAndKey($websiteId, $key, $value, $type, $description, $group);
        
        // Xóa cache
        self::clearSettingCache($websiteId, $key, $group);
    }

    /**
     * Lấy cài đặt theo nhóm của website cụ thể
     */
    public static function getWebsiteSettingsByGroup(int $websiteId, string $group): array
    {
        $cacheKey = self::CACHE_PREFIX . 'group_' . $websiteId . '_' . $group;
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($websiteId, $group) {
            return WebsiteSetting::getByWebsiteAndGroup($websiteId, $group);
        });
    }

    /**
     * Xóa cache cài đặt
     */
    public static function clearSettingCache(int $websiteId, string $key = null, string $group = null): void
    {
        if ($key) {
            Cache::forget(self::CACHE_PREFIX . 'setting_' . $websiteId . '_' . $key);
        }
        
        if ($group) {
            Cache::forget(self::CACHE_PREFIX . 'group_' . $websiteId . '_' . $group);
        }
        
        // Xóa cache website
        Cache::forget(self::CACHE_PREFIX . 'domain_*');
        Cache::forget(self::CACHE_PREFIX . 'code_*');
        Cache::forget(self::CACHE_PREFIX . 'active_all');
    }

    /**
     * Xóa tất cả cache website
     */
    public static function clearAllCache(): void
    {
        $keys = Cache::getRedis()->keys(self::CACHE_PREFIX . '*');
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }

    /**
     * Lấy URL đầy đủ của website
     */
    public static function getWebsiteUrl(Website $website, string $path = ''): string
    {
        $protocol = request()->isSecure() ? 'https://' : 'http://';
        $url = $protocol . $website->domain;
        
        if ($path) {
            $url .= '/' . ltrim($path, '/');
        }
        
        return $url;
    }

    /**
     * Kiểm tra xem có phải website hiện tại không
     */
    public static function isCurrentWebsite(Website $website): bool
    {
        $current = self::getCurrentWebsite();
        return $current && $current->id === $website->id;
    }

    /**
     * Lấy danh sách website cho dropdown
     */
    public static function getWebsiteOptions(): array
    {
        $websites = self::getActiveWebsites();
        $options = [];
        
        foreach ($websites as $website) {
            $options[$website['id']] = $website['name'] . ' (' . $website['domain'] . ')';
        }
        
        return $options;
    }
}
