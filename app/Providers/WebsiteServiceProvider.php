<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Helpers\WebsiteHelper;

class WebsiteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Đăng ký WebsiteHelper như singleton
        $this->app->singleton('website', function ($app) {
            return new WebsiteHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Tạo helper functions
        if (!function_exists('website')) {
            /**
             * Lấy instance WebsiteHelper
             */
            function website(): WebsiteHelper
            {
                return app('website');
            }
        }

        if (!function_exists('current_website')) {
            /**
             * Lấy website hiện tại
             */
            function current_website()
            {
                return WebsiteHelper::getCurrentWebsite();
            }
        }

        if (!function_exists('website_setting')) {
            /**
             * Lấy cài đặt website hiện tại
             */
            function website_setting(string $key, $default = null)
            {
                return WebsiteHelper::getSetting($key, $default);
            }
        }

        if (!function_exists('website_settings_group')) {
            /**
             * Lấy cài đặt theo nhóm của website hiện tại
             */
            function website_settings_group(string $group): array
            {
                return WebsiteHelper::getSettingsByGroup($group);
            }
        }

        if (!function_exists('set_website_setting')) {
            /**
             * Lưu cài đặt website hiện tại
             */
            function set_website_setting(
                string $key, 
                $value, 
                string $type = 'string', 
                string $description = null, 
                string $group = null
            ): void {
                WebsiteHelper::setSetting($key, $value, $type, $description, $group);
            }
        }
    }
}
