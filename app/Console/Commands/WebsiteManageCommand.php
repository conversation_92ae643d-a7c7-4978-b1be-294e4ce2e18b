<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Website;
use App\Models\WebsiteSetting;
use App\Helpers\WebsiteHelper;

class WebsiteManageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'website:manage 
                            {action : Action to perform (list|create|update|delete|settings|clear-cache)}
                            {--id= : Website ID}
                            {--name= : Website name}
                            {--domain= : Website domain}
                            {--code= : Website code}
                            {--description= : Website description}
                            {--active=1 : Website active status (1 or 0)}
                            {--default=0 : Set as default website (1 or 0)}
                            {--key= : Setting key}
                            {--value= : Setting value}
                            {--type=string : Setting type}
                            {--group= : Setting group}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage websites and their settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                return $this->listWebsites();
            case 'create':
                return $this->createWebsite();
            case 'update':
                return $this->updateWebsite();
            case 'delete':
                return $this->deleteWebsite();
            case 'settings':
                return $this->manageSettings();
            case 'clear-cache':
                return $this->clearCache();
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }
    }

    /**
     * List all websites
     */
    private function listWebsites()
    {
        $websites = Website::orderBy('sort_order')->orderBy('name')->get();

        if ($websites->isEmpty()) {
            $this->info('No websites found.');
            return 0;
        }

        $headers = ['ID', 'Name', 'Domain', 'Code', 'Active', 'Default', 'Sort Order'];
        $rows = [];

        foreach ($websites as $website) {
            $rows[] = [
                $website->id,
                $website->name,
                $website->domain,
                $website->code,
                $website->is_active ? 'Yes' : 'No',
                $website->is_default ? 'Yes' : 'No',
                $website->sort_order,
            ];
        }

        $this->table($headers, $rows);
        return 0;
    }

    /**
     * Create a new website
     */
    private function createWebsite()
    {
        $name = $this->option('name') ?: $this->ask('Website name');
        $domain = $this->option('domain') ?: $this->ask('Website domain');
        $code = $this->option('code') ?: $this->ask('Website code');
        $description = $this->option('description') ?: $this->ask('Website description (optional)', '');
        $isActive = (bool) $this->option('active');
        $isDefault = (bool) $this->option('default');

        try {
            $website = Website::create([
                'name' => $name,
                'domain' => $domain,
                'code' => $code,
                'description' => $description,
                'is_active' => $isActive,
                'is_default' => $isDefault,
                'sort_order' => Website::max('sort_order') + 1,
            ]);

            if ($isDefault) {
                $website->setAsDefault();
            }

            $this->info("Website '{$name}' created successfully with ID: {$website->id}");
            WebsiteHelper::clearAllCache();
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to create website: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Update an existing website
     */
    private function updateWebsite()
    {
        $id = $this->option('id') ?: $this->ask('Website ID');
        $website = Website::find($id);

        if (!$website) {
            $this->error("Website with ID {$id} not found.");
            return 1;
        }

        $data = [];
        
        if ($name = $this->option('name')) {
            $data['name'] = $name;
        }
        
        if ($domain = $this->option('domain')) {
            $data['domain'] = $domain;
        }
        
        if ($code = $this->option('code')) {
            $data['code'] = $code;
        }
        
        if ($description = $this->option('description')) {
            $data['description'] = $description;
        }
        
        if ($this->hasOption('active')) {
            $data['is_active'] = (bool) $this->option('active');
        }

        try {
            $website->update($data);

            if ((bool) $this->option('default')) {
                $website->setAsDefault();
            }

            $this->info("Website '{$website->name}' updated successfully.");
            WebsiteHelper::clearAllCache();
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to update website: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Delete a website
     */
    private function deleteWebsite()
    {
        $id = $this->option('id') ?: $this->ask('Website ID');
        $website = Website::find($id);

        if (!$website) {
            $this->error("Website with ID {$id} not found.");
            return 1;
        }

        if ($website->is_default) {
            $this->error("Cannot delete the default website.");
            return 1;
        }

        if ($this->confirm("Are you sure you want to delete website '{$website->name}'?")) {
            $website->delete();
            $this->info("Website '{$website->name}' deleted successfully.");
            WebsiteHelper::clearAllCache();
            return 0;
        }

        $this->info('Operation cancelled.');
        return 0;
    }

    /**
     * Manage website settings
     */
    private function manageSettings()
    {
        $id = $this->option('id') ?: $this->ask('Website ID');
        $website = Website::find($id);

        if (!$website) {
            $this->error("Website with ID {$id} not found.");
            return 1;
        }

        $key = $this->option('key');
        $value = $this->option('value');

        if (!$key) {
            // List all settings
            $settings = $website->settings()->orderBy('group')->orderBy('key')->get();
            
            if ($settings->isEmpty()) {
                $this->info("No settings found for website '{$website->name}'.");
                return 0;
            }

            $headers = ['Key', 'Value', 'Type', 'Group', 'Description'];
            $rows = [];

            foreach ($settings as $setting) {
                $rows[] = [
                    $setting->key,
                    strlen($setting->value) > 50 ? substr($setting->value, 0, 47) . '...' : $setting->value,
                    $setting->type,
                    $setting->group ?: 'N/A',
                    $setting->description ?: 'N/A',
                ];
            }

            $this->table($headers, $rows);
            return 0;
        }

        if ($value === null) {
            // Get setting value
            $settingValue = $website->getSetting($key);
            $this->info("Setting '{$key}' = " . json_encode($settingValue));
            return 0;
        }

        // Set setting value
        $type = $this->option('type') ?: 'string';
        $group = $this->option('group');
        $description = $this->ask('Setting description (optional)', '');

        try {
            $website->setSetting($key, $value, $type, $description, $group);
            $this->info("Setting '{$key}' updated successfully.");
            WebsiteHelper::clearAllCache();
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to update setting: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Clear website cache
     */
    private function clearCache()
    {
        WebsiteHelper::clearAllCache();
        $this->info('Website cache cleared successfully.');
        return 0;
    }
}
