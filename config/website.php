<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Website Management Configuration
    |--------------------------------------------------------------------------
    |
    | C<PERSON>u hình cho hệ thống quản lý nhiều website
    |
    */

    // Cache settings
    'cache' => [
        'enabled' => env('WEBSITE_CACHE_ENABLED', true),
        'ttl' => env('WEBSITE_CACHE_TTL', 3600), // 1 hour
        'prefix' => env('WEBSITE_CACHE_PREFIX', 'website_'),
    ],

    // Default website settings
    'default_settings' => [
        'general' => [
            'site_title' => 'Website Title',
            'site_description' => 'Website Description',
            'site_keywords' => 'website, keywords',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '0123456789',
        ],
        'seo' => [
            'seo_title' => 'SEO Title',
            'seo_description' => 'SEO Description',
            'google_analytics_id' => '',
            'google_tag_manager_id' => '',
            'facebook_pixel_id' => '',
        ],
        'social' => [
            'facebook_url' => '',
            'twitter_url' => '',
            'instagram_url' => '',
            'youtube_url' => '',
            'linkedin_url' => '',
        ],
        'contact' => [
            'address' => '',
            'working_hours' => '',
            'map_coordinates' => '',
        ],
    ],

    // Setting types and validation
    'setting_types' => [
        'string' => [
            'validation' => 'string|max:255',
            'cast' => 'string',
        ],
        'text' => [
            'validation' => 'string|max:65535',
            'cast' => 'string',
        ],
        'integer' => [
            'validation' => 'integer',
            'cast' => 'integer',
        ],
        'float' => [
            'validation' => 'numeric',
            'cast' => 'float',
        ],
        'boolean' => [
            'validation' => 'boolean',
            'cast' => 'boolean',
        ],
        'json' => [
            'validation' => 'array',
            'cast' => 'array',
        ],
        'url' => [
            'validation' => 'url|max:255',
            'cast' => 'string',
        ],
        'email' => [
            'validation' => 'email|max:255',
            'cast' => 'string',
        ],
        'phone' => [
            'validation' => 'string|max:20',
            'cast' => 'string',
        ],
        'color' => [
            'validation' => 'string|regex:/^#[a-fA-F0-9]{6}$/',
            'cast' => 'string',
        ],
        'image' => [
            'validation' => 'string|max:255',
            'cast' => 'string',
        ],
    ],

    // Setting groups
    'setting_groups' => [
        'general' => [
            'name' => 'Cài đặt chung',
            'description' => 'Các cài đặt cơ bản của website',
            'icon' => 'fas fa-cog',
            'order' => 1,
        ],
        'seo' => [
            'name' => 'SEO',
            'description' => 'Cài đặt tối ưu hóa công cụ tìm kiếm',
            'icon' => 'fas fa-search',
            'order' => 2,
        ],
        'social' => [
            'name' => 'Mạng xã hội',
            'description' => 'Liên kết mạng xã hội',
            'icon' => 'fas fa-share-alt',
            'order' => 3,
        ],
        'contact' => [
            'name' => 'Liên hệ',
            'description' => 'Thông tin liên hệ',
            'icon' => 'fas fa-phone',
            'order' => 4,
        ],
        'ecommerce' => [
            'name' => 'Thương mại điện tử',
            'description' => 'Cài đặt bán hàng',
            'icon' => 'fas fa-shopping-cart',
            'order' => 5,
        ],
        'payment' => [
            'name' => 'Thanh toán',
            'description' => 'Phương thức thanh toán',
            'icon' => 'fas fa-credit-card',
            'order' => 6,
        ],
        'news' => [
            'name' => 'Tin tức',
            'description' => 'Cài đặt tin tức và blog',
            'icon' => 'fas fa-newspaper',
            'order' => 7,
        ],
        'appearance' => [
            'name' => 'Giao diện',
            'description' => 'Cài đặt giao diện website',
            'icon' => 'fas fa-palette',
            'order' => 8,
        ],
        'advanced' => [
            'name' => 'Nâng cao',
            'description' => 'Cài đặt nâng cao',
            'icon' => 'fas fa-tools',
            'order' => 9,
        ],
    ],

    // Website validation rules
    'validation' => [
        'name' => 'required|string|max:255',
        'domain' => 'required|string|max:255|unique:websites,domain',
        'code' => 'required|string|max:50|unique:websites,code|regex:/^[a-z0-9_-]+$/',
        'description' => 'nullable|string|max:1000',
        'logo' => 'nullable|string|max:255',
        'favicon' => 'nullable|string|max:255',
        'config' => 'nullable|array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'sort_order' => 'integer|min:0|max:999',
    ],

    // Allowed file types for uploads
    'upload' => [
        'logo' => [
            'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'svg'],
            'max_size' => 2048, // KB
        ],
        'favicon' => [
            'allowed_types' => ['ico', 'png'],
            'max_size' => 512, // KB
        ],
    ],

    // Auto-detect website by domain
    'auto_detect' => [
        'enabled' => env('WEBSITE_AUTO_DETECT', true),
        'fallback_to_default' => env('WEBSITE_FALLBACK_DEFAULT', true),
    ],

    // Multi-language support
    'multi_language' => [
        'enabled' => env('WEBSITE_MULTI_LANGUAGE', false),
        'default_locale' => env('WEBSITE_DEFAULT_LOCALE', 'vi'),
        'supported_locales' => ['vi', 'en'],
    ],

    // Performance settings
    'performance' => [
        'lazy_load_settings' => env('WEBSITE_LAZY_LOAD_SETTINGS', true),
        'cache_settings_in_memory' => env('WEBSITE_CACHE_IN_MEMORY', false),
        'preload_default_website' => env('WEBSITE_PRELOAD_DEFAULT', true),
    ],

    // Security settings
    'security' => [
        'encrypt_sensitive_settings' => env('WEBSITE_ENCRYPT_SENSITIVE', false),
        'allowed_domains' => env('WEBSITE_ALLOWED_DOMAINS', ''),
        'block_suspicious_domains' => env('WEBSITE_BLOCK_SUSPICIOUS', false),
    ],
];
